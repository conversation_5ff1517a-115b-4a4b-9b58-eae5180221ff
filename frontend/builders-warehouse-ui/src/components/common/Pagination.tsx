import React from 'react';
import styled from 'styled-components';

// Styled components for pagination
const PaginationContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 0;
  width: 100%;
`;

const PaginationControls = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const PageButton = styled.button<{ $active?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2.5rem;
  height: 2.5rem;
  border: 1px solid ${props => props.$active ? '#042B41' : '#E5E7EB'};
  background-color: ${props => props.$active ? '#042B41' : 'white'};
  color: ${props => props.$active ? 'white' : '#374151'};
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-weight: ${props => props.$active ? '600' : '400'};
  cursor: pointer;
  padding: 0 0.625rem;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: ${props => props.$active ? '#042B41' : '#F9FAFB'};
    border-color: ${props => props.$active ? '#042B41' : '#D1D5DB'};
  }
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
    background-color: #F9FAFB;
    border-color: #E5E7EB;
    color: #9CA3AF;
  }
`;

const NavButton = styled(PageButton)`
  min-width: auto;
  padding: 0 0.75rem;
  display: flex;
  align-items: center;
  font-weight: 500;
`;

const PageInfo = styled.div`
  font-size: 0.875rem;
  color: #6B7280;
  display: flex;
  align-items: center;
`;

const ResultsPerPageContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const ResultsPerPageLabel = styled.span`
  font-size: 0.875rem;
  color: #6B7280;
`;

const ResultsPerPageSelect = styled.select`
  padding: 0.375rem 0.75rem;
  border: 1px solid #E5E7EB;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  color: #374151;
  background-color: white;
  cursor: pointer;
  
  &:focus {
    outline: none;
    border-color: #4B5563;
  }
`;

// Add styled component for ellipsis
const EllipsisSpan = styled.span`
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2.5rem;
  height: 2.5rem;
  color: #6B7280;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: default;
  user-select: none;
`;

// Props interface
interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  totalItems?: number;
  itemsPerPage?: number;
  onItemsPerPageChange?: (itemsPerPage: number) => void;
  itemsPerPageOptions?: number[];
  showItemsPerPage?: boolean;
}

/**
 * Reusable pagination component that handles navigation between pages
 * Designed to match the provided design with Previous/Next navigation
 * Shows only 2-3 page numbers at a time with ellipsis for better UX
 */
const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  totalItems,
  itemsPerPage = 10,
  onItemsPerPageChange,
  itemsPerPageOptions = [10, 20, 50],
  showItemsPerPage = true
}) => {
  // Generate the page buttons to display - improved algorithm
  const getPageNumbers = () => {
    const pageNumbers: (number | string)[] = [];
    
    // If total pages is 4 or less, show all pages
    if (totalPages <= 4) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }
    
    // Always show page 1
    pageNumbers.push(1);
    
    // Determine the range of pages to show around current page
    let startPage: number;
    let endPage: number;
    
    if (currentPage <= 3) {
      // Near the beginning: show 1, 2, 3, ..., last
      startPage = 2;
      endPage = 3;
    } else if (currentPage >= totalPages - 2) {
      // Near the end: show 1, ..., last-2, last-1, last
      startPage = totalPages - 2;
      endPage = totalPages - 1;
    } else {
      // In the middle: show 1, ..., current-1, current, current+1, ..., last
      startPage = currentPage - 1;
      endPage = currentPage + 1;
    }
    
    // Add ellipsis after page 1 if needed
    if (startPage > 2) {
      pageNumbers.push('...');
    }
    
    // Add the middle pages
    for (let i = startPage; i <= endPage; i++) {
      if (i > 1 && i < totalPages) {
        pageNumbers.push(i);
      }
    }
    
    // Add ellipsis before last page if needed
    if (endPage < totalPages - 1) {
      pageNumbers.push('...');
    }
    
    // Always show last page if it's different from page 1
    if (totalPages > 1) {
      pageNumbers.push(totalPages);
    }
    
    return pageNumbers;
  };

  const handleItemsPerPageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    if (onItemsPerPageChange) {
      onItemsPerPageChange(Number(e.target.value));
      // Reset to page 1 when changing items per page
      onPageChange(1);
    }
  };

  const pageNumbers = getPageNumbers();
  
  // Calculate item range for display
  const startItem = totalItems ? Math.min((currentPage - 1) * itemsPerPage + 1, totalItems) : 0;
  const endItem = totalItems ? Math.min(currentPage * itemsPerPage, totalItems) : 0;

  return (
    <PaginationContainer>
      {/* Results per page dropdown */}
      {showItemsPerPage && onItemsPerPageChange && (
        <ResultsPerPageContainer>
          <ResultsPerPageLabel>Show results:</ResultsPerPageLabel>
          <ResultsPerPageSelect
            value={itemsPerPage}
            onChange={handleItemsPerPageChange}
          >
            {itemsPerPageOptions.map(option => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </ResultsPerPageSelect>
        </ResultsPerPageContainer>
      )}
      
      {/* Page info - showing x of y results */}
      {totalItems !== undefined && (
        <PageInfo>
          Showing {startItem} to {endItem} of {totalItems} results
        </PageInfo>
      )}
      
      {/* Pagination controls */}
      <PaginationControls>
        {/* Previous button */}
        <NavButton
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          Previous
        </NavButton>
        
        {/* Page numbers */}
        {pageNumbers.map((page, index) => {
          if (page === '...') {
            return (
              <EllipsisSpan key={`ellipsis-${index}`}>
                ...
              </EllipsisSpan>
            );
          }
          
          return (
            <PageButton
              key={`page-${page}`}
              $active={Number(page) === currentPage}
              onClick={() => onPageChange(Number(page))}
            >
              {page}
            </PageButton>
          );
        })}
        
        {/* Next button */}
        <NavButton
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages || totalPages === 0}
        >
          Next
        </NavButton>
      </PaginationControls>
    </PaginationContainer>
  );
};

export default Pagination; 
